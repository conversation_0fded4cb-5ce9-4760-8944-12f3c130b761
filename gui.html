<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Data Analyzer GUI</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for better aesthetics and layout */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #1a202c; /* Dark background */
            color: #e2e8f0; /* Light text */
            display: flex;
            flex-direction: column;
            min-height: 70vh;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr; /* Default to single column for mobile */
            gap: 0.1rem;
            padding: 0.001rem;
            margin: 0%
            flex-grow: 0.1;
            grid-template-rows: minmax(0, 1fr); /* Constrain row height to prevent vertical expansion */
        }
        @media (min-width: 768px) {
            .container {
                grid-template-columns: 1fr 2fr; /* Two columns for tablet/desktop */
            }
        }
        @media (min-width: 1024px) {
            .container {
                grid-template-columns: 1fr 2fr 2fr; /* Three columns for larger desktops */
            }
        }
        .panel {
            background-color: #2d3748; /* Dark panel background */
            border-radius: 0.5rem; /* Rounded corners */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            padding: 0.1rem;
            display: flex;
            flex-direction: column;
            overflow: hidden; /* Ensure content doesn't spill */
        }
        .panel-header {
            font-weight: 600;
            font-size: 1.0rem;
            margin-bottom: 0.01rem;
            color: #f7fafc; /* Light header text */
            border-bottom: 0.1px solid #4a5568; /* Darker border */
            padding-bottom: 0.1rem;
        }
        .scrollable-content {
            flex-grow: 1;
            overflow-y: auto;
            padding-right: 0.1rem; /* Space for scrollbar */
        }
        /* Style for D3 flowchart nodes */
        .flowchart-node {
            fill: #3182ce; /* Darker Blue */
            stroke: #2c5282;
            stroke-width: 1px;
            cursor: pointer;
            transition: fill 0.3s ease;
        }
        .flowchart-node:hover {
            fill: #2b6cb0; /* Even darker blue on hover */
        }
        .flowchart-node-text {
            fill: white;
            font-size: 12px;
            text-anchor: middle;
            dominant-baseline: central;
            pointer-events: none; /* Allow clicks to pass through to the rectangle */
        }
        .flowchart-link {
            stroke: #718096; /* Darker Gray */
            stroke-width: 2px;
            marker-end: url(#arrowhead);
        }
        .prompt-entry {
            background-color: #3f4653; /* Mid-dark background for entries */
            border-radius: 0.5rem;
            padding: 0.3rem;
            margin-bottom: 0.5rem;
            word-wrap: break-word;
            font-size: 0.8rem;
        }
        .prompt-entry strong {
            color: #cbd5e0; /* Lighter gray for labels */
        }
        .prompt-entry pre {
            background-color: #000000; /* Dark background for code blocks */
            padding: 0.3rem;
            border-radius: 0.375rem;
            overflow-x: auto;
            margin-top: 0.2rem;
            font-size: 0.8rem; /* Smaller font size for prompts */
        }
        .btn {
            @apply px-4 py-2 rounded-lg font-semibold transition duration-200 ease-in-out;
            box-shadow: 0 2px 4px rgba(0.1, 0.1, 0.1, 1);
        }
        .btn-primary {
            @apply bg-blue-600 text-white hover:bg-blue-700;
        }
        .btn-secondary {
            @apply bg-gray-600 text-white hover:bg-gray-700;
        }
        .btn-danger {
            @apply bg-red-600 text-white hover:bg-red-700;
        }
        input[type="text"], textarea {
            @apply p-2 border border-gray-600 bg-gray-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
        }
        label {
            @apply block text-sm font-medium text-gray-300 mb-1;
        }
    </style>
    <!-- D3.js CDN for flowchart -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <!-- Chart.js CDN for plots -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-1000 min-h-screen flex flex-col">

    <header class="bg-gray-800 text-white p-4 shadow-md padding: 0.2rem;">
        <h1 class="text-2xl font-bold text-center">LLM-Orchestrated Data Analysis Pipeline GUI</h1>
    </header>

    <main class="container mx-auto mt-2 mb-2 flex-grow">
        <!-- Controls Area -->
        <div class="panel col-span-1">
            <h2 class="panel-header">Controls</h2>
            <div class="space-y-4 scrollable-content">
                <div>
                    <label for="objective-input">Analysis Objective:</label>
                    <textarea id="objective-input" class="w-full h-36 bg-gray-600 border-gray-800 text-white placeholder-white font-size: 0.5rem;" placeholder="e.g., Confirm presence of cyclic impulsive component related to inner race damage."></textarea>
                </div>
                <div>
                    <label for="data-description-input">Data Description:</label>
                    <textarea id="data-description-input" class="w-full h-36 bg-gray-600 border-gray-800 text-white placeholder-white" placeholder="e.g., Single-channel time-series, vibration signal, 50000 Hz sampling frequency."></textarea>
                </div>
                <div class="flex flex-col space-y-2">
                    <button id="start-analysis-btn" class="btn btn-primary">Start Analysis</button>
                    <button id="add-mock-step-btn" class="btn btn-secondary" disabled>Add Mock Step</button>
                    <button id="refine-last-step-btn" class="btn btn-secondary" disabled>Refine Last Step</button>
                    <button id="remove-last-step-btn" class="btn btn-danger" disabled>Remove Last Step</button>
                </div>
            </div>
        </div>

        <!-- Main Visualization Area -->
        <div class="panel col-span-1 md:col-span-2 lg:col-span-1">
            <h2 class="panel-header">Visualization</h2>
            <div class="relative flex-grow flex flex-col space-y-4 scrollable-content">
                <div class="p-1 rounded-lg relative bg-gray-700">
                    <h3 class="text-lg font-semibold mb-1">Action Sequence Flowchart</h3>
                    <svg id="flowchart-svg" class="w-full h-80 bg-gray-800 border border-gray-700 rounded-md"></svg>
                    <div class="absolute top-4 right-4 text-sm text-gray-500">
                        Click on nodes for details.
                    </div>
                </div>
                <div class="p-1 rounded-lg relative bg-gray-700">
                    <h3 class="text-lg font-semibold mb-1">Current Action Result Plot</h3>
                    <div class="relative w-full h-80 bg-gray-800 border border-gray-700 rounded-md">
                        <canvas id="result-plot-canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Text Field Area (Prompts Log) -->
        <div class="panel col-span-1 md:col-span-2 lg:col-span-1">
            <h2 class="panel-header">LLM Prompts & Responses</h2>
            <div class="relative flex-grow h-0">
                <div id="prompts-log" class="absolute inset-0 overflow-y-auto space-y-3 p-1">
                    <!-- Prompts will be appended here -->
                    <div class="prompt-entry">
                        <strong class="text-teal-300 font-size: 0.6rem;">System:</strong> Welcome! Enter your analysis objective and data description to begin.
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-gray-800 text-white p-3 text-center text-sm shadow-inner mt-4">
        &copy; 2025 LLM Data Analyzer. All rights reserved.
    </footer>

    <script>
        // Global variables for the GUI state
        let pipelineSteps = [];
        let currentPlotChart = null; // To hold the Chart.js instance

        // DOM Elements
        const objectiveInput = document.getElementById('objective-input');
        const dataDescriptionInput = document.getElementById('data-description-input');
        const startAnalysisBtn = document.getElementById('start-analysis-btn');
        const addMockStepBtn = document.getElementById('add-mock-step-btn');
        const refineLastStepBtn = document.getElementById('refine-last-step-btn');
        const removeLastStepBtn = document.getElementById('remove-last-step-btn');
        const flowchartSvg = d3.select("#flowchart-svg");
        const resultPlotCanvas = document.getElementById('result-plot-canvas');
        const promptsLog = document.getElementById('prompts-log');

        // --- Utility Functions ---

        // Function to add messages to the prompts log
        function addPromptToLog(type, content) {
            const entryDiv = document.createElement('div');
            entryDiv.className = 'prompt-entry';
            entryDiv.innerHTML = `<strong>${type}:</strong> <pre>${content}</pre>`;
            promptsLog.appendChild(entryDiv);
            promptsLog.scrollTop = promptsLog.scrollHeight; // Scroll to bottom
        }

        // Function to generate random data for plots
        function generateRandomPlotData() {
            const labels = Array.from({ length: 50 }, (_, i) => `Time ${i + 1}`);
            const data = Array.from({ length: 50 }, () => Math.random() * 100);
            return { labels, data };
        }

        // Function to render/update the plot
        function renderPlot(plotData, title = "Analysis Result") {
            if (currentPlotChart) {
                currentPlotChart.destroy(); // Destroy existing chart instance
            }

            const ctx = resultPlotCanvas.getContext('2d');
            currentPlotChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: plotData.labels,
                    datasets: [{
                        label: title,
                        data: plotData.data,
                        borderColor: '#63b3ed', // Lighter blue for line
                        backgroundColor: 'rgba(99, 179, 237, 0.2)', // Lighter blue for area
                        borderWidth: 2,
                        pointRadius: 0,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            display: false,
                            ticks: { color: '#a0aec0' },
                            grid: { color: '#4a5568' }
                        },
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#a0aec0' },
                            grid: { color: '#4a5568' }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false,
                            labels: { color: '#e2e8f0' }
                        },
                        title: {
                            display: true,
                            text: title,
                            font: { size: 16, weight: 'bold' },
                            color: '#e2e8f0'
                        }
                    }
                }
            });
        }

        // Function to render/update the D3 flowchart
        function renderFlowchart() {
            flowchartSvg.html(''); // Clear previous flowchart
            if (pipelineSteps.length === 0) {
                flowchartSvg.append("text")
                    .attr("x", "50%")
                    .attr("y", "50%")
                    .attr("text-anchor", "middle")
                    .attr("dominant-baseline", "central")
                    .attr("fill", "#718096")
                    .text("No steps yet. Start analysis!");
                return;
            }

            const nodeWidth = 100;
            const nodeHeight = 40;
            const horizontalSpacing = 50;
            const verticalSpacing = 20;

            const nodes = pipelineSteps.map((d, i) => ({
                id: d.action_id,
                name: d.tool_name,
                x: i * (nodeWidth + horizontalSpacing) + nodeWidth / 2,
                y: flowchartSvg.node().getBoundingClientRect().height / 2
            }));

            // Calculate SVG width needed
            const totalWidth = nodes.length * (nodeWidth + horizontalSpacing) - horizontalSpacing;
            flowchartSvg.attr("width", Math.max(totalWidth, flowchartSvg.node().parentElement.clientWidth));
            // The height is controlled by Tailwind's h-64 class, so we don't set it dynamically.

            // Define arrowhead marker
            flowchartSvg.append("defs").append("marker")
                .attr("id", "arrowhead")
                .attr("viewBox", "-0 -5 10 10")
                .attr("refX", 5)
                .attr("refY", 0)
                .attr("orient", "auto")
                .attr("markerWidth", 6)
                .attr("markerHeight", 6)
                .attr("xoverflow", "visible")
                .append("path")
                .attr("d", "M 0,-5 L 10,0 L 0,5")
                .attr("fill", "#718096")
                .style("stroke", "none");

            // Links
            flowchartSvg.selectAll(".flowchart-link")
                .data(d3.range(nodes.length - 1))
                .enter().append("line")
                .attr("class", "flowchart-link")
                .attr("x1", (d, i) => nodes[i].x + nodeWidth / 2)
                .attr("y1", (d, i) => nodes[i].y)
                .attr("x2", (d, i) => nodes[i+1].x - nodeWidth / 2)
                .attr("y2", (d, i) => nodes[i+1].y);

            // Nodes
            const nodeGroup = flowchartSvg.selectAll(".node-group")
                .data(nodes)
                .enter().append("g")
                .attr("class", "node-group")
                .attr("transform", d => `translate(${d.x - nodeWidth / 2}, ${d.y - nodeHeight / 2})`);

            nodeGroup.append("rect")
                .attr("width", nodeWidth)
                .attr("height", nodeHeight)
                .attr("rx", 8) /* Rounded corners */
                .attr("ry", 8)
                .attr("class", "flowchart-node")
                .on("click", (event, d) => {
                    const action = pipelineSteps.find(step => step.action_id === d.id);
                    if (action) {
                        addPromptToLog('System', `<strong>Details for Step ${action.action_id}:</strong><br>Tool: ${action.tool_name}<br>Parameters: ${JSON.stringify(action.params, null, 2)}<br>Output: ${action.output_variable}`);
                        renderPlot(action.plotData, `Result for ${action.tool_name}`);
                    }
                });

            const textNode = nodeGroup.append("text")
                .attr("class", "flowchart-node-text")
                .attr("x", nodeWidth / 2)
                .attr("y", nodeHeight / 2 - 5); // Adjust y for two lines

            textNode.append("tspan")
                .attr("x", nodeWidth / 2)
                .text(d => `Step ${d.id}:`);

            textNode.append("tspan")
                .attr("x", nodeWidth / 2)
                .attr("dy", "1.2em")
                .text(d => d.name);
        }

        // --- Event Handlers ---

        async function startAnalysis() {
            const objective = objectiveInput.value.trim();
            const dataDescription = dataDescriptionInput.value.trim();

            if (!objective || !dataDescription) {
                addPromptToLog('System', 'Please provide both an Analysis Objective and Data Description.');
                return;
            }

            addPromptToLog('System', `Starting analysis with objective: "${objective}" and data: "${dataDescription}"`);

            // Simulate LLM constructing Metaknowledge
            addPromptToLog('LLM Prompt (Metaknowledge)', `Given the objective "${objective}" and data description "${dataDescription}", construct a structured Metaknowledge JSON object.`);
            await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate delay
            const metaknowledge = {
                data_summary: { type: "time-series", domain: "vibration signal" },
                analysis_objective: { primary_goal: objective }
            };
            addPromptToLog('LLM Response (Metaknowledge)', JSON.stringify(metaknowledge, null, 2));

            // Simulate initial script generation
            addPromptToLog('LLM Prompt (Initial Script)', `Based on the metaknowledge, propose the initial logical step for data loading and cleaning.`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            pipelineSteps = [
                { action_id: 1, tool_name: "load_data", params: { file_path: "input.csv" }, output_variable: "raw_signal", plotData: generateRandomPlotData() },
                { action_id: 2, tool_name: "initial_cleaning", params: { signal: "raw_signal" }, output_variable: "cleaned_signal", plotData: generateRandomPlotData() }
            ];
            addPromptToLog('LLM Response (Initial Script)', `Proposed steps: ${pipelineSteps.map(s => s.tool_name).join(', ')}`);

            renderFlowchart();
            renderPlot(pipelineSteps[pipelineSteps.length - 1].plotData, `Result for ${pipelineSteps[pipelineSteps.length - 1].tool_name}`);

            // Enable action buttons
            addMockStepBtn.disabled = false;
            refineLastStepBtn.disabled = false;
            removeLastStepBtn.disabled = false;
            startAnalysisBtn.disabled = true; // Disable start button once started
            objectiveInput.disabled = true;
            dataDescriptionInput.disabled = true;
        }

        async function addMockStep() {
            const nextId = pipelineSteps.length + 1;
            const newToolName = `tool_${nextId}`;
            const newStep = {
                action_id: nextId,
                tool_name: newToolName,
                params: { input: pipelineSteps[pipelineSteps.length - 1]?.output_variable || "data", param1: Math.floor(Math.random() * 100) },
                output_variable: `output_${nextId}`,
                plotData: generateRandomPlotData()
            };

            addPromptToLog('LLM Prompt (Next Step)', `Current pipeline: ${pipelineSteps.map(s => s.tool_name).join(' -> ')}. Based on the last result and objective, what is the next logical step?`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            addPromptToLog('LLM Response (Next Step)', `Proposed new step: ${newToolName} with parameters ${JSON.stringify(newStep.params)}`);

            pipelineSteps.push(newStep);
            renderFlowchart();
            renderPlot(newStep.plotData, `Result for ${newToolName}`);
            addPromptToLog('System', `Step ${nextId} (${newToolName}) added to pipeline.`);
        }

        async function refineLastStep() {
            if (pipelineSteps.length === 0) {
                addPromptToLog('System', 'No steps to refine.');
                return;
            }

            const lastStep = pipelineSteps[pipelineSteps.length - 1];
            const originalParam = lastStep.params.param1;
            const newParam = Math.floor(Math.random() * 100);
            lastStep.params.param1 = newParam; // Simulate parameter refinement

            addPromptToLog('LLM Prompt (Refinement)', `The last step (${lastStep.tool_name}) with parameters ${JSON.stringify({ param1: originalParam })} did not yield optimal results. Suggest refinement for its parameters.`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            addPromptToLog('LLM Response (Refinement)', `Refining ${lastStep.tool_name}: changed param1 from ${originalParam} to ${newParam}.`);

            renderFlowchart(); // Flowchart doesn't change, but re-render to reflect potential internal state change
            lastStep.plotData = generateRandomPlotData(); // Simulate new plot after refinement
            renderPlot(lastStep.plotData, `Refined Result for ${lastStep.tool_name}`);
            addPromptToLog('System', `Last step (${lastStep.tool_name}) refined. New parameters: ${JSON.stringify(lastStep.params)}.`);
        }

        async function removeLastStep() {
            if (pipelineSteps.length === 0) {
                addPromptToLog('System', 'No steps to remove.');
                return;
            }

            const removedStep = pipelineSteps.pop();
            addPromptToLog('LLM Prompt (Self-Correction)', `The last step (${removedStep.tool_name}) was unproductive. Remove it and suggest a new strategy or alternative tool.`);
            await new Promise(resolve => setTimeout(resolve, 1000));
            addPromptToLog('LLM Response (Self-Correction)', `Removed ${removedStep.tool_name}. Re-evaluating strategy.`);

            renderFlowchart();
            if (pipelineSteps.length > 0) {
                const newLastStep = pipelineSteps[pipelineSteps.length - 1];
                renderPlot(newLastStep.plotData, `Result for ${newLastStep.tool_name}`);
            } else {
                if (currentPlotChart) {
                    currentPlotChart.destroy();
                    currentPlotChart = null;
                }
                resultPlotCanvas.getContext('2d').clearRect(0, 0, resultPlotCanvas.width, resultPlotCanvas.height);
                addPromptToLog('System', 'No steps left in pipeline.');
            }
            addPromptToLog('System', `Step ${removedStep.action_id} (${removedStep.tool_name}) removed from pipeline.`);
        }

        // --- Event Listeners ---
        startAnalysisBtn.addEventListener('click', startAnalysis);
        addMockStepBtn.addEventListener('click', addMockStep);
        refineLastStepBtn.addEventListener('click', refineLastStep);
        removeLastStepBtn.addEventListener('click', removeLastStep);

        // Initial render
        window.onload = function() {
            renderFlowchart();
            renderPlot(generateRandomPlotData(), "Initial Placeholder Plot");
        };

        // Handle window resize for responsive flowchart and plot
        window.addEventListener('resize', () => {
            renderFlowchart();
            if (pipelineSteps.length > 0 && currentPlotChart) {
                currentPlotChart.resize(); // Chart.js handles its own responsiveness
            }
        });

    </script>
</body>
</html>
