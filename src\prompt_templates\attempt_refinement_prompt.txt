**SYSTEM OPERATING PRINCIPLE: Refinement Strategy**
When a tool action is evaluated as "not useful," your first priority is to attempt a refinement by modifying its parameters. Only if refinement is not possible or also fails, you decide to remove the action entirely.

**TASK:** Refine a failed action.

The last action, `{action_summary}`, failed with the reasoning: "{failure_reasoning}".

The tool's documentation describes its parameters as follows:
{tool_documentation_parameters_section}

Based on the failure reasoning and the parameter descriptions, can you propose a new set of parameters to improve the result? Evaluate numeric and graphical results of the previous action before the current action, and try to assess how the values of parameters of the current action can be modified. 

Example: let's say that the current action is bandpass filtration. If previous action calculated spectrogram or a csc map, and it is possible to estimate the informative frequency band for a component of interest, maybe modification of cutoff frequencies of the filter in the current action would help achieve better results.

Respond with a complete, updated Action JSON object. If you believe no refinement is possible, respond with the single word "REMOVE".